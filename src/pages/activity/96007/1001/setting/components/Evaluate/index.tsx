import React, { useEffect, useImperativeHandle, useReducer, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { Button, DatePicker2, Field, Form, Grid, Input, Message, NumberPicker, Radio, Tag } from '@alifd/next';
import constant from '@/utils/constant';
import { FormLayout, PageData, evaluateGradeList } from '@/pages/activity/96007/1001/util';
import { activityEditDisabled, getParams } from '@/utils';
import styles from './index.module.scss';
import format from '@/utils/format';

const { Row, Col } = Grid;
const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const filterateList = ['#差', '#不好', '#垃圾', '#不值', '#再也不来', '#一般般', '#不舒服', '#赔了'];

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  // 评价商品
  const field: Field = Field.useField();
  // 日期改变，处理提交数据
  const onDataRangeChange = (evaluateTime): void => {
    setData({
      evaluateTime,
      evaluateStartTime: format.formatDateTimeDayjs(evaluateTime[0]),
      evaluateEndTime: format.formatDateTimeDayjs(evaluateTime[1]),
    });
  };

  // 负面评价
  const addEvaluateFilterate = (data) => {
    if (formData.evaluateFilterate.includes(data)) {
      return;
    }
    if ((formData.evaluateFilterate + data).length >= 20) {
      Message.error('负面词不能超过20个字');
      return;
    }
    formData.evaluateFilterate += data;
    setData({ evaluateFilterate: formData.evaluateFilterate });
    field.setError('evaluateFilterate', '');
  };

  // 修改关键词
  const changeWord = (value: string, index: number) => {
    // 校验里边有几组用#分隔的关键词，最多不能超过4组
    if(value.split('#').length > 5){
      Message.error('一组最多只能添加4个关键词');
      return;
    }
    // 用#分隔的每一个关键词不允许超过20个字
    if(value.split('#').some((item) => item.length > 20)){
      Message.error('关键词不能超过20个字');
      return;
    }
    const newEvaluateWord = [...formData.evaluateWord];
    newEvaluateWord[index] = value;
    setData({ evaluateWord: newEvaluateWord });
    field.setError(`evaluateWord${index+1}`, '');
  };

  // 添加关键字组
  const addWord = () => {
    const newEvaluateWord = [...formData.evaluateWord, ''];
    setData({ evaluateWord: newEvaluateWord });
  };

  // 删除关键字组
  const removeWord = (index: number) => {
    const newEvaluateWord = formData.evaluateWord.filter((_, i) => i !== index);
    setData({ evaluateWord: newEvaluateWord });
  };

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  // const handlePreview = (data) => {
  //   setData({ orderSkuListPreview: data });
  // };
  return (
    <LzPanel title="评价设置">
      <Form {...formItemLayout} disabled={activityEditDisabled()} field={field}>
        <FormItem label="评价时间" required requiredMessage="请选择评价时间">
          <RangePicker
            className="w-300"
            name="rangeDate"
            inputReadOnly
            format={dateFormat}
            hasClear={false}
            showTime
            value={formData.evaluateTime}
            onChange={onDataRangeChange}
          />
        </FormItem>
        <FormItem label="评价星级" required requiredMessage="选择评价星级">
          <RadioGroup value={formData.evaluateGrade} onChange={(evaluateGrade) => setData({ evaluateGrade })}>
            {evaluateGradeList.map((item) => {
              return (
                <Radio value={item.value} key={item.value}>
                  {item.label}
                </Radio>
              );
            })}
          </RadioGroup>
        </FormItem>
        <FormItem label="评价字数" required>
          <RadioGroup
            value={formData.evaluateNumberType}
            onChange={(evaluateNumberType: number) => setData({ evaluateNumberType })}
          >
            <Radio id="1" value={1}>
              不限制
            </Radio>
            <Radio id="2" value={2}>
              限制
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label=" " colon={false}>
          <Row gutter="4">
            <Col>
              {formData.evaluateNumberType === 2 && (
                <div className={styles.panel}>
                  <FormItem required requiredTrigger="onBlur" requiredMessage="请输入评价字数" style={{ margin: 0 }}>
                    <NumberPicker
                      name="evaluateNumber"
                      value={formData.evaluateNumber}
                      onChange={(evaluateNumber: number) => setData({ evaluateNumber })}
                      type="inline"
                      min={1}
                      max={9999999}
                      className={styles.number}
                    />
                    个字
                  </FormItem>
                </div>
              )}
              {formData.evaluateNumberType === 1 && <div className={styles.panel}>不限制评价字数</div>}
            </Col>
          </Row>
        </FormItem>
        <FormItem label="评价图片" required>
          <RadioGroup
            value={formData.evaluateImgType}
            onChange={(evaluateImgType: number) => setData({ evaluateImgType })}
          >
            <Radio id="1" value={1}>
              不限制
            </Radio>
            <Radio id="2" value={2}>
              限制
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label=" " colon={false}>
          <Row gutter="4">
            <Col>
              {formData.evaluateImgType === 2 && (
                <div className={styles.panel}>
                  <FormItem
                    required
                    requiredTrigger="onBlur"
                    requiredMessage="请输入评价图片数量"
                    style={{ margin: 0 }}
                  >
                    <NumberPicker
                      name="evaluateImg"
                      value={formData.evaluateImg}
                      onChange={(evaluateImg: number) => setData({ evaluateImg })}
                      type="inline"
                      min={1}
                      max={9999999}
                      className={styles.number}
                    />
                    张图片
                  </FormItem>
                </div>
              )}
              {formData.evaluateImgType === 1 && <div className={styles.panel}>不限制评价图片数量</div>}
            </Col>
          </Row>
        </FormItem>
        <FormItem
          label="评价视频"
          required
          extra={<div className="next-form-item-help">注：评价内容中图片和视频数量和最多为9，其中视频数量最少为1</div>}
        >
          <RadioGroup
            value={formData.evaluateVideoType}
            onChange={(evaluateVideoType: number) => setData({ evaluateVideoType })}
          >
            <Radio id="1" value={1}>
              不限制
            </Radio>
            <Radio id="2" value={2}>
              必须包含视频
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="负面词过滤" required>
          <RadioGroup
            value={formData.evaluateFilterateType}
            onChange={(evaluateFilterateType: number) => setData({ evaluateFilterateType })}
          >
            <Radio id="1" value={1}>
              不限制
            </Radio>
            <Radio id="2" value={2}>
              限制
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label=" " colon={false}>
          <Row gutter="4">
            <Col>
              {formData.evaluateFilterateType === 2 && (
                <div className={styles.panel}>
                  <FormItem required requiredTrigger="onBlur" requiredMessage="请输入评价文字" style={{ margin: 0 }}>
                    <div>
                      {filterateList.map((item, index) => {
                        return (
                          <Button
                            key={index}
                            type={'primary'}
                            text
                            onClick={() => addEvaluateFilterate(item)}
                            style={{ marginRight: '10px' }}
                          >
                            {item}
                          </Button>
                        );
                      })}
                    </div>
                    <Input
                      name="evaluateFilterate"
                      maxLength={20}
                      value={formData.evaluateFilterate}
                      placeholder="例如：#差#不好#垃圾"
                      onChange={(evaluateFilterate) => setData({ evaluateFilterate })}
                    />
                  </FormItem>
                </div>
              )}
              {formData.evaluateFilterateType === 1 && <div className={styles.panel}>不限制评价文字</div>}
            </Col>
          </Row>
        </FormItem>
        <FormItem label="关键字是否限制" required>
          <RadioGroup
            value={formData.evaluateWordType}
            onChange={(evaluateWordType: number) => setData({ evaluateWordType })}
          >
            <Radio id="1" value={1}>
              不限制
            </Radio>
            <Radio id="2" value={2}>
              限制
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label=" " colon={false}>
          <Row gutter="4">
            <Col>
              {formData.evaluateWordType === 2 && (
                <div className={styles.panel}>
                  {formData.evaluateWord.map((item: string, index: number) => {
                    return (
                      <FormItem key={index} required requiredTrigger="onBlur" requiredMessage="请输入评价关键字" style={{ marginBottom: '10px' }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <Input
                            name={`evaluateWord${index+1}`}
                            value={item}
                            label={`关键字组${index+1}`}
                            placeholder="例如：#长高吧少年二创大赛"
                            onChange={(value) => changeWord(value, index)}
                            style={{ flex: 1 }}
                          />
                          <Button
                            type="primary"
                            warning
                            size="small"
                            onClick={() => removeWord(index)}
                            disabled={formData.evaluateWord.length <= 1}
                          >
                            删除
                          </Button>
                        </div>
                      </FormItem>
                    );
                  })}
                  <Button type="primary" onClick={addWord} disabled={formData.evaluateWord.length >= 4}>
                    添加关键字组{`${formData.evaluateWord.length}/4`}
                  </Button>
                </div>
              )}
              {formData.evaluateWordType === 1 && <div className={styles.panel}>不限制评价文字</div>}
            </Col>
          </Row>
        </FormItem>
      </Form>
    </LzPanel>
  );
};
