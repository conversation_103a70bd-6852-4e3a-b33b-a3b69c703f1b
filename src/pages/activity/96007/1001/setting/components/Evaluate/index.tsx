import React, { useEffect, useImperativeHandle, useReducer } from 'react';
import LzPanel from '@/components/LzPanel';
import { Button, DatePicker2, Field, Form, Grid, Input, Message, NumberPicker, Radio } from '@alifd/next';
import constant from '@/utils/constant';
import { FormLayout, PageData, evaluateGradeList } from '@/pages/activity/96007/1001/util';
import { activityEditDisabled } from '@/utils';
import styles from './index.module.scss';
import format from '@/utils/format';

const { Row, Col } = Grid;
const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const filterateList = ['#差', '#不好', '#垃圾', '#不值', '#再也不来', '#一般般', '#不舒服', '#赔了'];

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 是否为编辑模式
  const isEditMode = getParams('type') === 'edit';

  // 原有的关键字组（编辑模式下从接口返回的历史数据）
  const [originalEvaluateWords, setOriginalEvaluateWords] = useState<string[]>([]);

  // 新增的关键字组（每个原有组对应的新增内容）
  const [newEvaluateWords, setNewEvaluateWords] = useState<string[]>([]);

  // 完全新增的关键字组
  const [pureNewGroups, setPureNewGroups] = useState<string[]>([]);

  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  useEffect((): void => {
    setFormData(value || defaultValue);

    // 编辑模式下初始化原有关键字组
    if (isEditMode && value && value.evaluateWord && value.evaluateWord.length > 0) {
      // 接口返回的数据就是原有的关键字组（历史数据）
      const originalWords = value.evaluateWord.filter(word => word && word.trim() !== '');
      setOriginalEvaluateWords(originalWords);
      // 为每个原有关键字组初始化一个空的新增输入框
      const newWordsForOriginal = new Array(originalWords.length).fill('');
      setNewEvaluateWords(newWordsForOriginal);
      // 初始化完全新增的组为空
      setPureNewGroups([]);
    } else {
      // 非编辑模式，所有关键字组都是新增的
      setOriginalEvaluateWords([]);
      setNewEvaluateWords([]);
      setPureNewGroups(value?.evaluateWord || ['']);
    }
  }, [value, isEditMode]);

  // 评价商品
  const field: Field = Field.useField();
  // 日期改变，处理提交数据
  const onDataRangeChange = (evaluateTime): void => {
    setData({
      evaluateTime,
      evaluateStartTime: format.formatDateTimeDayjs(evaluateTime[0]),
      evaluateEndTime: format.formatDateTimeDayjs(evaluateTime[1]),
    });
  };

  // 负面评价
  const addEvaluateFilterate = (data) => {
    if (formData.evaluateFilterate.includes(data)) {
      return;
    }
    if ((formData.evaluateFilterate + data).length >= 20) {
      Message.error('负面词不能超过20个字');
      return;
    }
    formData.evaluateFilterate += data;
    setData({ evaluateFilterate: formData.evaluateFilterate });
    field.setError('evaluateFilterate', '');
  };

  // 更新formData中的evaluateWord
  const updateFormData = () => {
    if (!isEditMode) {
      // 非编辑模式，直接使用完全新增的组
      setData({ evaluateWord: pureNewGroups });
    } else {
      // 编辑模式，合并原有关键字和新增关键字
      const mergedWords = originalEvaluateWords.map((originalWord, i) => {
        const newWord = newEvaluateWords[i] || '';
        if (newWord.trim() === '') {
          return originalWord;
        }
        return originalWord + newWord;
      });
      // 添加完全新增的关键字组
      const validPureNewWords = pureNewGroups.filter(word => word.trim() !== '');
      setData({ evaluateWord: [...mergedWords, ...validPureNewWords] });
    }
  };

  // 修改原有关键字组的新增内容
  const changeNewWordForOriginal = (value: string, index: number) => {
    // 用#分隔的每一个关键词不允许超过20个字
    if(value.split('#').some((item) => item.length > 20)){
      Message.error('关键词不能超过20个字');
      return;
    }

    // 检查与原有关键字的总数
    const originalKeywordsInGroup = originalEvaluateWords[index].split('#').filter(k => k.trim() !== '').length;
    const newKeywordsInGroup = value.split('#').filter(k => k.trim() !== '').length;
    if (originalKeywordsInGroup + newKeywordsInGroup > 4) {
      Message.error(`该组关键字总数不能超过4个（已有${originalKeywordsInGroup}个）`);
      return;
    }

    const updatedNewWords = [...newEvaluateWords];
    updatedNewWords[index] = value;
    setNewEvaluateWords(updatedNewWords);

    field.setError(`newEvaluateWord${index + 1}`, '');
  };

  // 修改完全新增的关键字组
  const changePureNewWord = (value: string, index: number) => {
    // 用#分隔的每一个关键词不允许超过20个字
    if(value.split('#').some((item) => item.length > 20)){
      Message.error('关键词不能超过20个字');
      return;
    }

    // 检查单组不超过4个
    if (value.split('#').filter(k => k.trim() !== '').length > 4) {
      Message.error('一组最多只能添加4个关键词');
      return;
    }

    const updatedPureNewGroups = [...pureNewGroups];
    updatedPureNewGroups[index] = value;
    setPureNewGroups(updatedPureNewGroups);

    field.setError(`pureNewWord${index + 1}`, '');
  };

  // 当状态变化时更新formData
  useEffect(() => {
    if (!isEditMode) {
      // 非编辑模式，直接使用完全新增的组
      setData({ evaluateWord: pureNewGroups });
    } else {
      // 编辑模式，合并原有关键字和新增关键字
      const mergedWords = originalEvaluateWords.map((originalWord, i) => {
        const newWord = newEvaluateWords[i] || '';
        if (newWord.trim() === '') {
          return originalWord;
        }
        return originalWord + newWord;
      });
      // 添加完全新增的关键字组
      const validPureNewWords = pureNewGroups.filter(word => word.trim() !== '');
      setData({ evaluateWord: [...mergedWords, ...validPureNewWords] });
    }
  }, [originalEvaluateWords, newEvaluateWords, pureNewGroups, isEditMode]);

  // 添加完全新增的关键字组
  const addPureNewWord = () => {
    // 计算当前实际的关键字组数量
    const currentGroups = originalEvaluateWords.length + pureNewGroups.length;
    if (currentGroups >= 4) {
      Message.error('最多只能有4个关键字组');
      return;
    }

    const updatedPureNewGroups = [...pureNewGroups, ''];
    setPureNewGroups(updatedPureNewGroups);
  };

  // 删除完全新增的关键字组
  const removePureNewWord = (index: number) => {
    if (!isEditMode && pureNewGroups.length <= 1) {
      Message.error('至少需要保留一个关键字组');
      return;
    }

    if (isEditMode && pureNewGroups.length <= 1 && originalEvaluateWords.length === 0) {
      Message.error('至少需要保留一个关键字组');
      return;
    }

    const updatedPureNewGroups = pureNewGroups.filter((_, i) => i !== index);
    setPureNewGroups(updatedPureNewGroups);
  };

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  // const handlePreview = (data) => {
  //   setData({ orderSkuListPreview: data });
  // };
  return (
    <LzPanel title="评价设置">
      <Form {...formItemLayout} disabled={activityEditDisabled()} field={field}>
        <FormItem label="评价时间" required requiredMessage="请选择评价时间">
          <RangePicker
            className="w-300"
            name="rangeDate"
            inputReadOnly
            format={dateFormat}
            hasClear={false}
            showTime
            value={formData.evaluateTime}
            onChange={onDataRangeChange}
          />
        </FormItem>
        <FormItem label="评价星级" required requiredMessage="选择评价星级">
          <RadioGroup value={formData.evaluateGrade} onChange={(evaluateGrade) => setData({ evaluateGrade })}>
            {evaluateGradeList.map((item) => {
              return (
                <Radio value={item.value} key={item.value}>
                  {item.label}
                </Radio>
              );
            })}
          </RadioGroup>
        </FormItem>
        <FormItem label="评价字数" required>
          <RadioGroup
            value={formData.evaluateNumberType}
            onChange={(evaluateNumberType: number) => setData({ evaluateNumberType })}
          >
            <Radio id="1" value={1}>
              不限制
            </Radio>
            <Radio id="2" value={2}>
              限制
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label=" " colon={false}>
          <Row gutter="4">
            <Col>
              {formData.evaluateNumberType === 2 && (
                <div className={styles.panel}>
                  <FormItem required requiredTrigger="onBlur" requiredMessage="请输入评价字数" style={{ margin: 0 }}>
                    <NumberPicker
                      name="evaluateNumber"
                      value={formData.evaluateNumber}
                      onChange={(evaluateNumber: number) => setData({ evaluateNumber })}
                      type="inline"
                      min={1}
                      max={9999999}
                      className={styles.number}
                    />
                    个字
                  </FormItem>
                </div>
              )}
              {formData.evaluateNumberType === 1 && <div className={styles.panel}>不限制评价字数</div>}
            </Col>
          </Row>
        </FormItem>
        <FormItem label="评价图片" required>
          <RadioGroup
            value={formData.evaluateImgType}
            onChange={(evaluateImgType: number) => setData({ evaluateImgType })}
          >
            <Radio id="1" value={1}>
              不限制
            </Radio>
            <Radio id="2" value={2}>
              限制
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label=" " colon={false}>
          <Row gutter="4">
            <Col>
              {formData.evaluateImgType === 2 && (
                <div className={styles.panel}>
                  <FormItem
                    required
                    requiredTrigger="onBlur"
                    requiredMessage="请输入评价图片数量"
                    style={{ margin: 0 }}
                  >
                    <NumberPicker
                      name="evaluateImg"
                      value={formData.evaluateImg}
                      onChange={(evaluateImg: number) => setData({ evaluateImg })}
                      type="inline"
                      min={1}
                      max={9999999}
                      className={styles.number}
                    />
                    张图片
                  </FormItem>
                </div>
              )}
              {formData.evaluateImgType === 1 && <div className={styles.panel}>不限制评价图片数量</div>}
            </Col>
          </Row>
        </FormItem>
        <FormItem
          label="评价视频"
          required
          extra={<div className="next-form-item-help">注：评价内容中图片和视频数量和最多为9，其中视频数量最少为1</div>}
        >
          <RadioGroup
            value={formData.evaluateVideoType}
            onChange={(evaluateVideoType: number) => setData({ evaluateVideoType })}
          >
            <Radio id="1" value={1}>
              不限制
            </Radio>
            <Radio id="2" value={2}>
              必须包含视频
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="负面词过滤" required>
          <RadioGroup
            value={formData.evaluateFilterateType}
            onChange={(evaluateFilterateType: number) => setData({ evaluateFilterateType })}
          >
            <Radio id="1" value={1}>
              不限制
            </Radio>
            <Radio id="2" value={2}>
              限制
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label=" " colon={false}>
          <Row gutter="4">
            <Col>
              {formData.evaluateFilterateType === 2 && (
                <div className={styles.panel}>
                  <FormItem required requiredTrigger="onBlur" requiredMessage="请输入评价文字" style={{ margin: 0 }}>
                    <div>
                      {filterateList.map((item, index) => {
                        return (
                          <Button
                            key={index}
                            type={'primary'}
                            text
                            onClick={() => addEvaluateFilterate(item)}
                            style={{ marginRight: '10px' }}
                          >
                            {item}
                          </Button>
                        );
                      })}
                    </div>
                    <Input
                      name="evaluateFilterate"
                      maxLength={20}
                      value={formData.evaluateFilterate}
                      placeholder="例如：#差#不好#垃圾"
                      onChange={(evaluateFilterate) => setData({ evaluateFilterate })}
                    />
                  </FormItem>
                </div>
              )}
              {formData.evaluateFilterateType === 1 && <div className={styles.panel}>不限制评价文字</div>}
            </Col>
          </Row>
        </FormItem>
        <FormItem label="关键字是否限制" required>
          <RadioGroup
            value={formData.evaluateWordType}
            onChange={(evaluateWordType: number) => setData({ evaluateWordType })}
          >
            <Radio id="1" value={1}>
              不限制
            </Radio>
            <Radio id="2" value={2}>
              限制
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label=" " colon={false}>
          <Row gutter="4">
            <Col>
              {formData.evaluateWordType === 2 && (
                <div className={styles.panel}>
                  {/* 编辑模式下显示原有关键字组 */}
                  {isEditMode && originalEvaluateWords.map((item: string, index: number) => (
                    <FormItem key={`original-${index}`} style={{ marginBottom: '15px' }}>
                      <div style={{ marginBottom: '8px' }}>
                        <span style={{ fontSize: '14px', color: '#666', marginBottom: '4px', display: 'block' }}>
                          关键字组{index + 1}（历史关键字）:
                        </span>
                        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px', marginBottom: '8px' }}>
                          {item.split('#').filter(keyword => keyword.trim() !== '').map((keyword, keyIndex) => (
                            <Tag key={keyIndex} type="primary" size="small">
                              {keyword}
                            </Tag>
                          ))}
                        </div>
                      </div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <Input
                          name={`newEvaluateWord${index+1}`}
                          value={newEvaluateWords[index] || ''}
                          label={`新增关键字`}
                          placeholder="例如：#新关键字1#新关键字2"
                          onChange={(value) => changeNewWordForOriginal(value, index)}
                          style={{ flex: 1 }}
                        />
                        <span style={{ color: '#999', fontSize: '12px', whiteSpace: 'nowrap' }}>
                          不可删除
                        </span>
                      </div>
                    </FormItem>
                  ))}

                  {/* 完全新增的关键字组 */}
                  {pureNewGroups.map((item: string, index: number) => (
                    <FormItem key={`pure-new-${index}`} required requiredTrigger="onBlur" requiredMessage="请输入评价关键字" style={{ marginBottom: '10px' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <Input
                          name={`pureNewWord${index+1}`}
                          value={item}
                          label={`关键字组${originalEvaluateWords.length + index + 1}`}
                          placeholder="例如：#长高吧少年二创大赛"
                          onChange={(value) => changePureNewWord(value, index)}
                          style={{ flex: 1 }}
                        />
                        <Button
                          type="primary"
                          warning
                          size="small"
                          onClick={() => removePureNewWord(index)}
                          disabled={(!isEditMode && pureNewGroups.length <= 1) || (isEditMode && pureNewGroups.length <= 1 && originalEvaluateWords.length === 0)}
                        >
                          删除
                        </Button>
                      </div>
                    </FormItem>
                  ))}

                  <Button
                    type="primary"
                    onClick={addPureNewWord}
                    disabled={(originalEvaluateWords.length + pureNewGroups.length) >= 4}
                  >
                    添加关键字组{`${originalEvaluateWords.length + pureNewGroups.length}/4`}
                  </Button>
                </div>
              )}
              {formData.evaluateWordType === 1 && <div className={styles.panel}>不限制评价文字</div>}
            </Col>
          </Row>
        </FormItem>
      </Form>
    </LzPanel>
  );
};
